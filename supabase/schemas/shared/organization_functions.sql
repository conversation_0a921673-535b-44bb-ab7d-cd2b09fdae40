-- Organization Management Functions
-- This file contains functions for organization operations
CREATE OR REPLACE FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) RETURNS "uuid" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_org_id UUID;
BEGIN
	INSERT INTO public.organization (name, description, logo_url, created_by_user_id)
	VALUES (name, description, logo_url, auth.uid())
	RETURNING org_id INTO v_org_id;
	
	RETURN v_org_id;
END;
$$;

ALTER FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."create_organization" (
	"name" "text",
	"description" "text",
	"logo_url" "text"
) IS 'Creates a new organization and returns its ID';

CREATE OR REPLACE FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") RETURNS TABLE (
	"org_id" "uuid",
	"name" "text",
	"description" "text",
	"logo_url" "text",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT o.org_id, o.name, o.description, o.logo_url, o.created_by_user_id, o.created_at, o.updated_at
	FROM public.organization o
	WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('organization'::public.entity_type, o.org_id);
END;
$$;

ALTER FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_organization_by_name" ("org_name_param" "text") IS 'Gets organization details by name for users with access';
