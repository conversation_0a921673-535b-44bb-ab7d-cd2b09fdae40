-- Member Access Functions
-- This file contains functions for getting member information and access
CREATE OR REPLACE FUNCTION "public"."get_client_members" ("_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text",
	"role" "public"."membership_role"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT p.user_id, p.email, p.full_name, m.role
	FROM public.profile p
	JOIN public.membership m ON p.user_id = m.user_id
	JOIN public.client c ON m.entity_id = c.client_id
	WHERE c.name = _client_name
	AND m.entity_type = 'client'
	AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id);
END;
$$;

ALTER FUNCTION "public"."get_client_members" ("_client_name" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_client_members" ("_client_name" "text") IS 'Gets all members of a client by client name';

CREATE OR REPLACE FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") RETURNS TABLE (
	"client_id" "uuid",
	"name" "text",
	"description" "text",
	"user_role" "public"."membership_role"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT 
		c.client_id,
		c.name,
		c.description,
		public.get_effective_role(auth.uid(), 'client'::public.entity_type, c.client_id) as user_role
	FROM public.client c
	JOIN public.organization o ON c.org_id = o.org_id
	WHERE o.name = org_name_param
	AND public.current_user_has_entity_access('client'::public.entity_type, c.client_id);
END;
$$;

ALTER FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."get_clients_with_permissions" ("org_name_param" "text") IS 'Gets clients with user permissions for an organization';

CREATE OR REPLACE FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT DISTINCT p.user_id, p.email, p.full_name
	FROM public.profile p
	WHERE public.has_entity_access(
		p.user_id,
		'client'::public.entity_type,
		(SELECT client_id FROM public.client WHERE name = _client_name)
	)
	AND public.current_user_has_entity_access(
		'client'::public.entity_type,
		(SELECT client_id FROM public.client WHERE name = _client_name)
	);
END;
$$;

ALTER FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."profiles_with_client_access" ("_client_name" "text") IS 'Gets all profiles with access to a specific client';

CREATE OR REPLACE FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") RETURNS TABLE (
	"user_id" "uuid",
	"email" "text",
	"full_name" "text"
) LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
	RETURN QUERY
	SELECT DISTINCT p.user_id, p.email, p.full_name
	FROM public.profile p
	WHERE public.has_entity_access(
		p.user_id,
		'project'::public.entity_type,
		(SELECT p.project_id 
		 FROM public.project p 
		 JOIN public.client c ON p.client_id = c.client_id 
		 WHERE p.name = _project_name AND c.name = _client_name)
	)
	AND public.current_user_has_entity_access(
		'project'::public.entity_type,
		(SELECT p.project_id 
		 FROM public.project p 
		 JOIN public.client c ON p.client_id = c.client_id 
		 WHERE p.name = _project_name AND c.name = _client_name)
	);
END;
$$;

ALTER FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") OWNER TO "postgres";

COMMENT ON FUNCTION "public"."profiles_with_project_access" ("_project_name" "text", "_client_name" "text") IS 'Gets all profiles with access to a specific project';
