-- Project Stage Management Functions
-- This file contains functions for project stage operations
CREATE OR REPLACE FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) RETURNS boolean LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
DECLARE
	v_project_id UUID;
	v_is_ready BOOLEAN;
BEGIN
	-- Get project_id for permission check
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;
	
	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Project stage not found';
	END IF;
	
	-- Check if user can modify this project
	IF NOT public.can_modify_project(v_project_id) THEN
		RAISE EXCEPTION 'Insufficient permissions to complete this project stage';
	END IF;
	
	-- Check if stage is ready for completion
	SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;
	
	IF NOT v_is_ready THEN
		RAISE EXCEPTION 'Project stage has incomplete checklist items and cannot be completed';
	END IF;
	
	-- Update the stage
	UPDATE public.project_stage
	SET 
		status = 'Completed',
		completion_date = now(),
		completion_notes = p_completion_notes,
		updated_at = now()
	WHERE project_stage_id = p_project_stage_id;
	
	RETURN TRUE;
END;
$$;

ALTER FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) OWNER TO "postgres";

COMMENT ON FUNCTION "public"."complete_project_stage" (
	"p_project_stage_id" "uuid",
	"p_completion_notes" "text"
) IS 'Completes a project stage if all checklist items are complete';
