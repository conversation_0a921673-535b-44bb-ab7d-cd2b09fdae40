-- Project Gateway Stage Info Audit Table Schema
-- Audit log of all changes to project gateway stage info
-- Project Gateway Stage Info Audit table
CREATE TABLE IF NOT EXISTS "public"."project_gateway_stage_info_audit" (
	"audit_id" "uuid" DEFAULT "gen_random_uuid" () NOT NULL,
	"operation_type" "text" NOT NULL,
	"changed_by" "uuid" NOT NULL,
	"changed_at" timestamp with time zone DEFAULT "now" () NOT NULL,
	"old_values" "jsonb",
	"new_values" "jsonb",
	"project_gateway_stage_info_id" "uuid",
	"project_stage_id" "uuid",
	"basement_floors" numeric(15, 2),
	"ground_floor" numeric(15, 2),
	"upper_floors" numeric(15, 2),
	"total_gross_internal_floor_area" numeric(15, 2),
	"usable_area" numeric(15, 2),
	"circulation_area" numeric(15, 2),
	"ancillary_areas" numeric(15, 2),
	"internal_divisions" numeric(15, 2),
	"spaces_not_enclosed" numeric(15, 2),
	"total_gross_internal_floor_area_2" numeric(15, 2),
	"internal_cube" numeric(15, 2),
	"area_of_lowest_floor" numeric(15, 2),
	"site_area" numeric(15, 2),
	"number_of_units" numeric(15, 2),
	"nr_of_storeys" numeric(15, 2),
	"nr_of_storeys_primary" numeric(15, 2),
	"nr_of_storeys_secondary" numeric(15, 2),
	"basement_storeys_included_above" numeric(15, 2),
	"average_storey_height" numeric(15, 2),
	"below_ground_floors" numeric(15, 2),
	"ground_floor_height" numeric(15, 2),
	"above_ground_floors" numeric(15, 2),
	"external_vertical_envelope" numeric(15, 2),
	"additional_data" "jsonb",
	"created_by_user_id" "uuid",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "project_gateway_stage_info_audit_operation_type_check" CHECK (
		(
			"operation_type" = ANY (
				ARRAY[
					'INSERT'::"text",
					'UPDATE'::"text",
					'DELETE'::"text"
				]
			)
		)
	)
);

ALTER TABLE "public"."project_gateway_stage_info_audit" OWNER TO "postgres";

COMMENT ON TABLE "public"."project_gateway_stage_info_audit" IS 'Audit log of all changes to project gateway stage info';

-- Primary key constraint
ALTER TABLE ONLY "public"."project_gateway_stage_info_audit"
ADD CONSTRAINT "project_gateway_stage_info_audit_pkey" PRIMARY KEY ("audit_id");

-- Foreign key constraints
ALTER TABLE ONLY "public"."project_gateway_stage_info_audit"
ADD CONSTRAINT "project_gateway_stage_info_audit_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT;

-- Indexes for performance
CREATE INDEX "project_gateway_stage_info_audit_changed_at_idx" ON "public"."project_gateway_stage_info_audit" USING "btree" ("changed_at");

CREATE INDEX "project_gateway_stage_info_audit_project_gateway_stage_info_id_idx" ON "public"."project_gateway_stage_info_audit" USING "btree" ("project_gateway_stage_info_id");

CREATE INDEX "project_gateway_stage_info_audit_changed_by_idx" ON "public"."project_gateway_stage_info_audit" USING "btree" ("changed_by");

-- Enable Row Level Security
ALTER TABLE "public"."project_gateway_stage_info_audit" ENABLE ROW LEVEL SECURITY;

-- Project Gateway Stage Info Audit Function
CREATE OR REPLACE FUNCTION "public"."audit_project_gateway_stage_info_changes" () RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
SET
	"search_path" TO '' AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'DELETE', auth.uid(), NOW(), to_jsonb(OLD),
            OLD.project_gateway_stage_info_id, OLD.project_stage_id, OLD.basement_floors, OLD.ground_floor, OLD.upper_floors,
            OLD.total_gross_internal_floor_area, OLD.usable_area, OLD.circulation_area, OLD.ancillary_areas, OLD.internal_divisions,
            OLD.spaces_not_enclosed, OLD.total_gross_internal_floor_area_2, OLD.internal_cube, OLD.area_of_lowest_floor,
            OLD.site_area, OLD.number_of_units, OLD.nr_of_storeys, OLD.nr_of_storeys_primary, OLD.nr_of_storeys_secondary,
            OLD.basement_storeys_included_above, OLD.average_storey_height, OLD.below_ground_floors, OLD.ground_floor_height,
            OLD.above_ground_floors, OLD.external_vertical_envelope, OLD.additional_data, OLD.created_by_user_id,
            OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'UPDATE', auth.uid(), NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.project_gateway_stage_info_audit (
            operation_type, changed_by, changed_at, new_values,
            project_gateway_stage_info_id, project_stage_id, basement_floors, ground_floor, upper_floors,
            total_gross_internal_floor_area, usable_area, circulation_area, ancillary_areas, internal_divisions,
            spaces_not_enclosed, total_gross_internal_floor_area_2, internal_cube, area_of_lowest_floor,
            site_area, number_of_units, nr_of_storeys, nr_of_storeys_primary, nr_of_storeys_secondary,
            basement_storeys_included_above, average_storey_height, below_ground_floors, ground_floor_height,
            above_ground_floors, external_vertical_envelope, additional_data, created_by_user_id,
            created_at, updated_at
        ) VALUES (
            'INSERT', auth.uid(), NOW(), to_jsonb(NEW),
            NEW.project_gateway_stage_info_id, NEW.project_stage_id, NEW.basement_floors, NEW.ground_floor, NEW.upper_floors,
            NEW.total_gross_internal_floor_area, NEW.usable_area, NEW.circulation_area, NEW.ancillary_areas, NEW.internal_divisions,
            NEW.spaces_not_enclosed, NEW.total_gross_internal_floor_area_2, NEW.internal_cube, NEW.area_of_lowest_floor,
            NEW.site_area, NEW.number_of_units, NEW.nr_of_storeys, NEW.nr_of_storeys_primary, NEW.nr_of_storeys_secondary,
            NEW.basement_storeys_included_above, NEW.average_storey_height, NEW.below_ground_floors, NEW.ground_floor_height,
            NEW.above_ground_floors, NEW.external_vertical_envelope, NEW.additional_data, NEW.created_by_user_id,
            NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."audit_project_gateway_stage_info_changes" () OWNER TO "postgres";

COMMENT ON FUNCTION "public"."audit_project_gateway_stage_info_changes" () IS 'Audit trigger function for project_gateway_stage_info table';

-- Audit trigger for project_gateway_stage_info table
CREATE OR REPLACE TRIGGER "audit_project_gateway_stage_info_trigger"
AFTER INSERT
OR
UPDATE
OR DELETE ON "public"."project_gateway_stage_info" FOR EACH ROW
EXECUTE FUNCTION "public"."audit_project_gateway_stage_info_changes" ();

-- Row Level Security Policies
CREATE POLICY "System can insert project gateway stage info audit records" ON "public"."project_gateway_stage_info_audit" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Users can view project gateway stage info audit for accessible projects" ON "public"."project_gateway_stage_info_audit" FOR
SELECT
	TO "authenticated" USING (
		(
			SELECT
				"public"."can_access_project" (
					(
						SELECT
							"project_stage"."project_id"
						FROM
							"public"."project_stage"
						WHERE
							(
								"project_stage"."project_stage_id" = "project_gateway_stage_info_audit"."project_stage_id"
							)
					)
				) AS "can_access_project"
		)
	);
